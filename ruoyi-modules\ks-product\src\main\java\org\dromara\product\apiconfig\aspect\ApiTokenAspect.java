package org.dromara.product.apiconfig.aspect;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.product.apiconfig.annotation.RequireApiToken;
import org.dromara.product.apiconfig.service.IApiTokenValidationService;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 产品模块API Token验证切面
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Aspect
@Component("productApiTokenAspect")
@Order(0)
@RequiredArgsConstructor
public class ApiTokenAspect {

    private final IApiTokenValidationService apiTokenValidationService;

    @Around("@annotation(requireApiToken)")
    public Object around(ProceedingJoinPoint joinPoint, RequireApiToken requireApiToken) throws Throwable {
        try {
            // 提取API Token
            String apiToken = extractApiToken();

            if (apiToken == null || apiToken.trim().isEmpty()) {
                throw new ServiceException("API Token不能为空");
            }

            // 验证API Token并获取用户ID
            Long memberId = apiTokenValidationService.getMemberIdByApiToken(apiToken);

            if (memberId == null) {
                throw new ServiceException("无效的API Token");
            }

            // 将用户ID设置到SecurityContext中
            UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(memberId.toString(), null, null);
            SecurityContextHolder.getContext().setAuthentication(authentication);

            log.debug("API Token验证成功: memberId={}", memberId);

            // 执行目标方法
            return joinPoint.proceed();

        } catch (Exception e) {
            log.warn("API Token验证失败: {}", e.getMessage());
            throw new ServiceException("API Token验证失败: " + e.getMessage());
        } finally {
            // 清理SecurityContext
            SecurityContextHolder.clearContext();
        }
    }

    /**
     * 提取API Token
     */
    private String extractApiToken() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }

        HttpServletRequest request = attributes.getRequest();

        // 尝试从X-API-Key请求头获取
        String apiToken = request.getHeader("X-API-Key");
        if (apiToken != null && !apiToken.trim().isEmpty()) {
            return apiToken.trim();
        }

        // 尝试从Authorization请求头获取
        String authorization = request.getHeader("Authorization");
        if (authorization != null && authorization.startsWith("Bearer ")) {
            return authorization.substring(7).trim();
        }

        return null;
    }
}
