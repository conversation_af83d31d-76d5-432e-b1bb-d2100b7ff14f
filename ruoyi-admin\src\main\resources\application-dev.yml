--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: false
  url: http://localhost:9090/admin
  instance:
    service-host-type: IP
    metadata:
      username: ${spring.boot.admin.client.username}
      userpassword: ${spring.boot.admin.client.password}
  username: @monitor.username@
  password: @monitor.password@

--- # snail-job 配置
snail-job:
  enabled: false
  # 需要在 SnailJob 后台组管理创建对应名称的组,然后创建任务的时候选择对应的组,才能正确分派任务
  group: "ruoyi_group"
  # SnailJob 接入验证令牌 详见 script/sql/ry_job.sql `sj_group_config` 表
  token: "SJ_cKqBTPzCsWA3VyuCfFoccmuIEGXjr5KT"
  server:
    host: 127.0.0.1
    port: 17888
  # 命名空间UUID 详见 script/sql/ry_job.sql `sj_namespace`表`unique_id`字段
  namespace: ${spring.profiles.active}
  # 随主应用端口漂移
  port: 2${server.port}
  # 客户端ip指定
  host:
  # RPC类型: netty, grpc
  rpc-type: grpc

--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: org.postgresql.Driver
          url: jdbc:postgresql://**************:5432/ks?useUnicode=true&characterEncoding=utf8&useSSL=true&autoReconnect=true&reWriteBatchedInserts=true
          username: postgres
          password: postgres
#        # 从库数据源
#        slave:
#          lazy: true
#          type: ${spring.datasource.type}
#          driverClassName: com.mysql.cj.jdbc.Driver
#          url: **********************************************************************************************************************************************************************************************************************************************************
#          username:
#          password:
#        oracle:
#          type: ${spring.datasource.type}
#          driverClassName: oracle.jdbc.OracleDriver
#          url: *************************************
#          username: ROOT
#          password: root
#        postgres:
#          type: ${spring.datasource.type}
#          driverClassName: org.postgresql.Driver
#          url: ******************************************************************************************************************************************
#          username: root
#          password: root
#        sqlserver:
#          type: ${spring.datasource.type}
#          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          url: *******************************************************************************************************************
#          username: SA
#          password: root
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # redis 密码必须配置
    # password: ruoyi123
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称 不能用中文
    clientName: RuoYi-Vue-Plus
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # mail 邮件发送
mail:
  enabled: false
  host: smtp.163.com
  port: 465
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: xxxxxxxxxx
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: true
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0

--- # sms 短信 支持 阿里云 腾讯云 云片 等等各式各样的短信服务商
# https://sms4j.com/doc3/ 差异配置文档地址 支持单厂商多配置，可以配置多个同时使用
sms:
  # 配置源类型用于标定配置来源(interface,yaml)
  config-type: yaml
  # 用于标定yml中的配置是否开启短信拦截，接口配置不受此限制
  restricted: true
  # 短信拦截限制单手机号每分钟最大发送，只对开启了拦截的配置有效
  minute-max: 1
  # 短信拦截限制单手机号每日最大发送量，只对开启了拦截的配置有效
  account-max: 30
  # 以下配置来自于 org.dromara.sms4j.provider.config.BaseConfig类中
  blends:
    # 腾讯云短信配置 - 主要配置
    tencent:
      # 厂商标识：腾讯云
      supplier: tencent
      # 腾讯云SecretId
      access-key-id: AKIDVtLjwzpRqBJG4g5GDxUoywnYltpX1Rem
      # 腾讯云SecretKey
      access-key-secret: znNnz594oOtO4b6f4GKMJ6i7MPGvOfjb
      # 短信签名
      signature: 云南天度软件系统有限公司
      # 短信应用ID
      sdk-app-id: **********
      # 地域信息，默认为ap-beijing
      territory: ap-beijing
    # 阿里云短信配置 - 备用配置
    alibaba:
      # 厂商标识：阿里云
      supplier: alibaba
      # 阿里云AccessKeyId
      access-key-id: 您的AccessKeyId
      # 阿里云AccessKeySecret
      access-key-secret: 您的AccessKeySecret
      # 短信签名
      signature: 您的短信签名

# 短信模板配置（扩展SMS4J配置）
# 注意：这里使用不同的配置键避免与SMS4J冲突
member-sms:
  # 默认短信服务商
  default-provider: tencent
  # 验证码有效期（分钟）
  code-expire-minutes: 5
  # 发送间隔时间（秒）
  send-interval-seconds: 60
  # 腾讯云短信模板ID配置
  tencent:
    # 登录验证码模板：您的登录验证码是{1}，{2}分钟内有效，请勿泄露给他人
    login: "2487387"
    # 注册验证码模板：您的注册验证码是{1}，{2}分钟内有效，请勿泄露给他人
    register: "2487391"
    # 重置密码验证码模板：您的重置密码验证码是{1}，{2}分钟内有效，请勿泄露给他人
    #reset: "1234569"
  # 阿里云短信模板ID配置（备用）
  alibaba:
    # 登录验证码模板
    login: "SMS_123456789"
    # 注册验证码模板
    register: "SMS_123456790"
    # 重置密码验证码模板
    reset: "SMS_123456791"

--- # 三方授权
justauth:
  # 前端外网访问地址（使用ngrok公网地址）
  address: http://localhost:7013
  type:
    maxkey:
      # maxkey 服务器地址
      # 注意 如下均配置均不需要修改 maxkey 已经内置好了数据
      server-url: http://sso.maxkey.top
      client-id: 876892492581044224
      client-secret: x1Y5MTMwNzIwMjMxNTM4NDc3Mzche8
      redirect-uri: ${justauth.address}/social-callback?source=maxkey
    topiam:
      # topiam 服务器地址
      server-url: http://127.0.0.1:1898/api/v1/authorize/y0q************spq***********8ol
      client-id: 449c4*********937************759
      client-secret: ac7***********1e0************28d
      redirect-uri: ${justauth.address}/social-callback?source=topiam
      scopes: [openid, email, phone, profile]
    qq:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=qq
      union-id: false
    weibo:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=weibo
    gitee:
      client-id: 91436b7940090d09c72c7daf85b959cfd5f215d67eea73acbf61b6b590751a98
      client-secret: 02c6fcfd70342980cd8dd2f2c06c1a350645d76c754d7a264c4e125f9ba915ac
      redirect-uri: ${justauth.address}/social-callback?source=gitee
    dingtalk:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=dingtalk
    baidu:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=baidu
    csdn:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=csdn
    coding:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=coding
      coding-group-name: xx
    oschina:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=oschina
    alipay_wallet:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=alipay_wallet
      alipay-public-key: MIIB**************DAQAB
    wechat_open:
      # 开发阶段模拟配置 - 用于测试OAuth流程
      client-id: dev_wechat_mock_appid
      client-secret: dev_wechat_mock_secret
      redirect-uri: http://localhost:5173/auth/oauth/callback?source=wechat_open
    wechat_mp:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=wechat_mp
    wechat_enterprise:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=wechat_enterprise
      agent-id: 1000002
    gitlab:
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=gitlab
    gitea:
      # 前端改动 https://gitee.com/JavaLionLi/plus-ui/pulls/204
      # gitea 服务器地址
      server-url: https://demo.gitea.com
      client-id: 10**********6
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${justauth.address}/social-callback?source=gitea
    feishu:
      client-id: cli_a8036637af72900e
      client-secret: 5RQgSeSqrP5CrE7c8d9t2j4V1mafrfp8
      redirect-uri: ${justauth.address}/auth/feishu/oauth/callback

# 支付配置
payment:
  alipay:
    enabled: true
    app-id: 2021005176637688
    encrypt-type: AES
    encrypt-key: d3u1Nu28FW4Lu7KbmxhwVw==
    private-key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCM81jOAMQaNpDiMLnI40JBVqglklbChD3lpzFq3tguZpQ4dh9iPCbGBxk9u6L7dIjOVGypp9AHTa2+wl9pG2H2CTtGdMXoY2tVPIAz6OFNRv4xkgVT459uuVeIQe+CTcX3YIboK6ADLCBSimSvT4E8GpVT/u/XI3zVaHPvBBHIpAVHsc2fHojoHm462hg7e1a+lv8AplfMSVeclpZeUY4RlJbZDZ6aTQU3K2i/lODxeNVMok/s6MU4S+2s9PyuWKNsr+P8TcHtBnhinq86KoCS4NeJBepsCQJfxTWwxdUrNoxX/s0Y0pij0LvaxbNIoIVjO7liUzT+1cTyumT+ohgTAgMBAAECggEAfm975a7COlFgPcY/oXhaYon9Y1aWBP7jFzKXHaqweU8C/nLXTCNxFIohlkLOvImYfp00rCsgbKy9gjSRqZMqmK1HQrhVH1SvX1JaHjZ0hAF+27CEnu+8gHI5lryuwFBLmNA5s+IOM0pN5DzIfInw/51I/oPdWTs4K6b6+BjKaxN8qCLY/hND8aJkWXiGoGNHOBdpD1XRdN9h0qlibAu+g5xr+zdr+v60YrR46tAZ6ABGogefy35wZkGUTPzvo4/KJSxApS2hhEGUQiKXIZzuhdyZ51UeNk5oNCX4kzutXQqQEniaVBBhsMIc2wB+Qeba6FZQB9yJ9pMwi59iZvVHYQKBgQDib4PQDLcS8kWnQQ3/c1twDZ0qpERmxrZEOOyzIfu99Mgtbmh7lLl+eqe+h9V8g/YoVZeg18HX//FnY4hkE2prAbepzE7QoWJx7plV38irkMYqBR7Ob61zzRxArxTTjfz0xTMz51zlogij6eBxg/m/7TPME7PjbqhSuUbg+MwDJQKBgQCfWorBs059wooAhvQAkOZMv8mgoiGYEjMErd62mmut8K6X0jNluWS1QU8ztIwBKMADH9fvkeBY5f1/nyuR7oZG0ZtGLr/OXPVrcnDZwvH8tdUTDE+8U2UyI5uBvjYC88clxhKO1/yHQ5tocc3bvMp1VuGfPslS+lsWJ8EbPX1k1wKBgB7I5nTMpb8vrQSWNZ77Qf0z/KAlgR8v+OvBiXy5LA6hTu3OSuoySB3H8zJWKVIqZrcJG/pEGxiQtXNG9u4ULa2qqBuvqS94IXg35iTkrdWVczuXQp7yU/SgeCzbIMu6TB/DB7kQxXJ62w3d6CPGu5e6WgZuz1AmZUAJ7Hb9nE75AoGAX+sICn/7hwdKOfcBeDW/6gKnQuFUh0ytGOvvWsqodhKhhREpjtmBHPkqy1ypySjWKwtH1xRfjiMh62fLuhNxD7+rTxtkqLqbnwlLlEgbkJmCn+yRMjKgfwWf3QC5Uh2aicon8YNgpaq0KLIXz2RcGTUqqcgJNsErgrJ7MgJZMBECgYAnws6ZEXLChuZfoQgXvQpeg+dg8ZTfZ3tREwCdNkfocYvANXNmwBYcxwdz3MTS/6n5OAEo6AF71dx08KtOeiBFHlFPq5GhB6VCCRLa72+JMeCfc9PNEYzClIpxnlbLnxktLFYneT8nO8t6OuschSG4FO3uk0a4l9CJZRo0sr0QEw==
    alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnE8Ux+VKG08ULae7VbnPzlrizAR5mXSevsklBufPOA5GWWf4D78Agtro78mqpdy40GabaO2WylozE9LAap6MZXG2mfL2SvZeQS1MrYCmnT9rQkNGVzhW0AxSdWr8TCoptTUmiDL1OSqQ5hLREG3QNfWYn5mHDTH13hBkBZzMsRJUIbCf/MT/72bxXiQMhBGEZyLc8PFmY75ARRQuMrIaE82cLXDC9MEJbhsK/OLn3/5Ri8oav/QPiYr8NoJ3fUAa0oCSG6z69nTZwJ0uEHbhEz3Yt8WE+HFxLSage+tuOy8nvRM0IbmeavlAMpdw5XcGZMOI971gojm31A7gGCexNwIDAQAB
    server-url: https://openapi.alipay.com/gateway.do
    notify-url: https://ksun.chat/payment/callback/alipay

  wechat:
    enabled: true
    app-id: wxbbebfd37c3fa7ecf
    mch-id: 1620322418
    api-v3-key: 8WmafaDBCEQE5bTlca9n3xJWNs6ashlQ
    #private-key-path: classpath:certs/apiclient_key.pem
    #public-key-path: classpath:certs/pub_key.pem
    public-key: PUB_KEY_ID_0116203224182025072100291890000201
    merchant-serial-number: 4450EB33997D667F28D9F0B8F332227A3436CFAC
    notify-url: https://ksun.chat/payment/callback/wechat
    domain: https://api.mch.weixin.qq.com
