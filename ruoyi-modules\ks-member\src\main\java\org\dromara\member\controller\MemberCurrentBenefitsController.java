package org.dromara.member.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.member.domain.vo.TMemberCurrentBenefitsVo;
import org.dromara.member.service.IMemberCurrentBenefitsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 会员当前权益管理控制器
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/current-benefits")
@Tag(name = "会员当前权益管理", description = "会员当前权益管理相关接口")
public class MemberCurrentBenefitsController extends BaseController {

    private final IMemberCurrentBenefitsService memberCurrentBenefitsService;

    /**
     * 查询会员当前权益列表
     */
    @GetMapping("/list/{memberId}")
    @Operation(summary = "查询会员当前权益列表")
    public R<List<TMemberCurrentBenefitsVo>> list(@PathVariable Long memberId) {
        List<TMemberCurrentBenefitsVo> list = memberCurrentBenefitsService.getCurrentBenefits(memberId);
        return R.ok(list);
    }

    /**
     * 查询会员指定类型的权益
     */
    @GetMapping("/list/{memberId}/{benefitType}")
    @Operation(summary = "查询会员指定类型的权益")
    public R<List<TMemberCurrentBenefitsVo>> listByType(@PathVariable Long memberId,
                                                        @PathVariable String benefitType) {
        List<TMemberCurrentBenefitsVo> list = memberCurrentBenefitsService.getBenefitsByType(memberId, benefitType);
        return R.ok(list);
    }

    /**
     * 同步会员权益
     */
    @PostMapping("/sync/{memberId}")
    @Operation(summary = "同步会员权益")
    @Log(title = "同步会员权益", businessType = BusinessType.UPDATE)
    public R<Void> syncMemberBenefits(@PathVariable Long memberId) {
        Boolean result = memberCurrentBenefitsService.syncMemberBenefits(memberId);
        return result ? R.ok() : R.fail("同步失败");
    }

    /**
     * 批量同步所有会员权益
     */
    @PostMapping("/sync-all")
    @Operation(summary = "批量同步所有会员权益")
    @Log(title = "批量同步会员权益", businessType = BusinessType.UPDATE)
    public R<Void> syncAllMemberBenefits() {
        Boolean result = memberCurrentBenefitsService.syncAllMemberBenefits();
        return result ? R.ok() : R.fail("批量同步失败");
    }

    /**
     * 清理会员权益
     */
    @DeleteMapping("/clear/{memberId}")
    @Operation(summary = "清理会员权益")
    @Log(title = "清理会员权益", businessType = BusinessType.DELETE)
    public R<Void> clearMemberBenefits(@PathVariable Long memberId) {
        Boolean result = memberCurrentBenefitsService.clearMemberBenefits(memberId);
        return result ? R.ok() : R.fail("清理失败");
    }

    /**
     * 标记过期权益
     */
    @PostMapping("/mark-expired")
    @Operation(summary = "标记过期权益")
    @Log(title = "标记过期权益", businessType = BusinessType.UPDATE)
    public R<Void> markExpiredBenefits() {
        Boolean result = memberCurrentBenefitsService.markExpiredBenefits();
        return result ? R.ok() : R.fail("标记失败");
    }

    /**
     * 获取会员权益显示数据
     */
    @GetMapping("/display/{memberId}")
    @Operation(summary = "获取会员权益显示数据")
    public R<List<Map<String, Object>>> getMemberBenefitsForDisplay(@PathVariable Long memberId) {
        List<Map<String, Object>> benefits = memberCurrentBenefitsService.getMemberBenefitsForDisplay(memberId);
        return R.ok(benefits);
    }

    /**
     * 检查会员是否有指定权益
     */
    @GetMapping("/check/{memberId}")
    @Operation(summary = "检查会员是否有指定权益")
    public R<Boolean> hasBenefit(@PathVariable Long memberId,
                                @RequestParam String benefitType,
                                @RequestParam String benefitName) {
        Boolean result = memberCurrentBenefitsService.hasBenefit(memberId, benefitType, benefitName);
        return R.ok(result);
    }

    /**
     * 获取会员指定权益的配置值
     */
    @GetMapping("/config/{memberId}")
    @Operation(summary = "获取会员指定权益的配置值")
    public R<String> getBenefitConfig(@PathVariable Long memberId,
                                     @RequestParam String benefitType,
                                     @RequestParam String benefitName) {
        String config = memberCurrentBenefitsService.getBenefitConfig(memberId, benefitType, benefitName);
        return R.ok(config);
    }

}
