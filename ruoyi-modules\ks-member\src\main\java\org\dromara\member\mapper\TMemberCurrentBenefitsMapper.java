package org.dromara.member.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.member.domain.entity.TMemberCurrentBenefits;
import org.dromara.member.domain.vo.TMemberCurrentBenefitsVo;

import java.util.List;

/**
 * 会员当前权益Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface TMemberCurrentBenefitsMapper extends BaseMapperPlus<TMemberCurrentBenefits, TMemberCurrentBenefitsVo> {

    /**
     * 查询会员当前生效的权益
     */
    @Select("SELECT * FROM t_member_current_benefits " +
            "WHERE member_id = #{memberId} AND status = 'active' " +
            "AND (expiry_date IS NULL OR expiry_date > NOW()) AND del_flag = '0' " +
            "ORDER BY id")
    List<TMemberCurrentBenefits> selectActiveBenefitsByMemberId(@Param("memberId") Long memberId);

    /**
     * 查询会员指定配置ID的权益
     */
    @Select("SELECT * FROM t_member_current_benefits " +
            "WHERE member_id = #{memberId} AND benefit_config_id = #{benefitConfigId} " +
            "AND status = 'active' AND (expiry_date IS NULL OR expiry_date > NOW()) " +
            "AND del_flag = '0' ORDER BY id")
    List<TMemberCurrentBenefits> selectBenefitsByMemberIdAndConfigId(@Param("memberId") Long memberId,
                                                                     @Param("benefitConfigId") Long benefitConfigId);

    /**
     * 删除会员的所有当前权益
     */
    @Select("DELETE FROM t_member_current_benefits WHERE member_id = #{memberId}")
    int deleteByMemberId(@Param("memberId") Long memberId);

    /**
     * 标记过期的权益
     */
    @Select("UPDATE t_member_current_benefits SET status = 'expired', update_time = NOW() " +
            "WHERE expiry_date IS NOT NULL AND expiry_date <= NOW() AND status = 'active'")
    int markExpiredBenefits();

}
