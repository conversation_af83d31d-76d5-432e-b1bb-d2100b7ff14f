package org.dromara.billing.utils;

import org.dromara.billing.domain.dto.MemberInfo;

/**
 * API Token上下文工具类
 * 用于在请求处理过程中传递验证后的会员信息
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
public class ApiTokenContext {

    private static final ThreadLocal<MemberInfo> MEMBER_INFO_HOLDER = new ThreadLocal<>();

    /**
     * 设置当前请求的会员信息
     *
     * @param memberInfo 会员信息
     */
    public static void setMemberInfo(MemberInfo memberInfo) {
        MEMBER_INFO_HOLDER.set(memberInfo);
    }

    /**
     * 获取当前请求的会员信息
     *
     * @return 会员信息，如果未设置则返回null
     */
    public static MemberInfo getMemberInfo() {
        return MEMBER_INFO_HOLDER.get();
    }

    /**
     * 获取当前请求的会员ID
     *
     * @return 会员ID，如果未设置则返回null
     */
    public static Long getMemberId() {
        MemberInfo memberInfo = getMemberInfo();
        return memberInfo != null ? memberInfo.getMemberId() : null;
    }

    /**
     * 清除当前请求的会员信息
     */
    public static void clear() {
        MEMBER_INFO_HOLDER.remove();
    }

    /**
     * 检查是否已设置会员信息
     *
     * @return 如果已设置返回true，否则返回false
     */
    public static boolean hasMemberInfo() {
        return MEMBER_INFO_HOLDER.get() != null;
    }
}
