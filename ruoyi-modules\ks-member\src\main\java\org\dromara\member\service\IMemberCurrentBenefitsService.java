package org.dromara.member.service;

import org.dromara.member.domain.vo.TMemberCurrentBenefitsVo;

import java.util.List;
import java.util.Map;

/**
 * 会员当前权益Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IMemberCurrentBenefitsService {

    /**
     * 查询会员当前生效的权益
     */
    List<TMemberCurrentBenefitsVo> getCurrentBenefits(Long memberId);

    /**
     * 查询会员指定类型的权益
     */
    List<TMemberCurrentBenefitsVo> getBenefitsByType(Long memberId, String benefitType);

    /**
     * 同步会员权益到当前权益表
     */
    Boolean syncMemberBenefits(Long memberId);

    /**
     * 批量同步所有会员权益
     */
    Boolean syncAllMemberBenefits();

    /**
     * 清理会员的当前权益
     */
    Boolean clearMemberBenefits(Long memberId);

    /**
     * 标记过期的权益
     */
    Boolean markExpiredBenefits();

    /**
     * 获取会员权益的简化视图（用于前端显示）
     */
    List<Map<String, Object>> getMemberBenefitsForDisplay(Long memberId);

    /**
     * 检查会员是否有指定权益
     */
    Boolean hasBenefit(Long memberId, String benefitType, String benefitName);

    /**
     * 获取会员指定权益的配置值
     */
    String getBenefitConfig(Long memberId, String benefitType, String benefitName);

}
