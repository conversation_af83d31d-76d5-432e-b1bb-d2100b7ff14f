package org.dromara.member.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会员当前权益视图对象 t_member_current_benefits
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@ExcelIgnoreUnannotated
public class TMemberCurrentBenefitsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long memberId;

    /**
     * 权益配置ID
     */
    @ExcelProperty(value = "权益配置ID")
    private Long benefitConfigId;

    /**
     * 订阅ID
     */
    @ExcelProperty(value = "订阅ID")
    private Long subscriptionId;

    /**
     * 生效时间
     */
    @ExcelProperty(value = "生效时间")
    private Date effectiveTime;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    private Date expiryDate;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 自动续费
     */
    @ExcelProperty(value = "自动续费")
    private Integer autoRenew;

    /**
     * 同步状态
     */
    @ExcelProperty(value = "同步状态")
    private Integer syncStatus;

    /**
     * 最后同步时间
     */
    @ExcelProperty(value = "最后同步时间")
    private Date lastSyncTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    // ========== 扩展字段，用于前端显示 ==========

    /**
     * 权益类型（从配置表获取）
     */
    private String benefitType;

    /**
     * 权益名称（从配置表获取）
     */
    private String benefitName;

    /**
     * 权益配置（从配置表获取）
     */
    private String benefitConfig;

    /**
     * 权益值（从配置表获取）
     */
    private String benefitValue;

}
