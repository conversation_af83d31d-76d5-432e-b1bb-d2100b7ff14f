package org.dromara.product.apiconfig.domain.bo;

import org.dromara.product.apiconfig.domain.TApiCallLog;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * API调用日志业务对象 t_api_call_log
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TApiCallLog.class, reverseConvertGenerate = false)
public class TApiCallLogBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 链路追踪ID
     */
    private String traceId;

    /**
     * API配置ID
     */
    private Long apiConfigId;

    /**
     * 调用用户ID
     */
    private Long userId;

    /**
     * 请求参数
     */
    private Map<String, Object> requestParams;

    /**
     * 请求头
     */
    private Map<String, Object> requestHeaders;

    /**
     * 响应数据
     */
    private Object responseData;

    /**
     * HTTP响应状态码
     */
    private Integer responseStatus;

    /**
     * 执行时间（毫秒）
     */
    private Integer executionTimeMs;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间范围查询 - 开始时间
     */
    private Date createTimeStart;

    /**
     * 创建时间范围查询 - 结束时间
     */
    private Date createTimeEnd;

    /**
     * API代码（用于查询）
     */
    private String apiCode;

    /**
     * API名称（用于查询）
     */
    private String apiName;

    /**
     * 是否成功（用于查询）
     */
    private Boolean success;
}
