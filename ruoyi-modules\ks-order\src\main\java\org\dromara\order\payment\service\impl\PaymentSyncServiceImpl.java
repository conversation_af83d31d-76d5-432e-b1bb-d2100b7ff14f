package org.dromara.order.payment.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.type.KSLogType;
import org.dromara.order.log.service.ITOrderLogService;
import org.dromara.order.order.domain.TOrder;
import org.dromara.order.order.mapper.TOrderMapper;
import org.dromara.order.order.type.TOrderType;
import org.dromara.order.payment.domain.PaymentQueryResult;
import org.dromara.order.payment.domain.TPaymentRecord;
import org.dromara.order.payment.enums.PaymentType;
import org.dromara.order.payment.factory.PaymentServiceFactory;
import org.dromara.order.payment.service.IPaymentService;
import org.dromara.order.payment.service.IPaymentSyncService;
import org.dromara.order.payment.service.ITPaymentRecordService;
import org.dromara.order.payment.type.TPaymentRecordType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 支付状态同步服务实现
 * 独立的服务类，用于解决Spring AOP自调用问题
 *
 * @Author： zhanxd
 * @Date： 2025/7/28 17:38
 * @Describe：
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentSyncServiceImpl implements IPaymentSyncService {

    private final TOrderMapper orderMapper;
    private final ITPaymentRecordService paymentRecordService;
    private final PaymentServiceFactory paymentServiceFactory;
    private final ITOrderLogService orderLogService;

    /**
     * 检查并同步支付状态 - 独立事务
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean checkAndSyncPaymentStatus(Long orderId) {
        log.info("=== 开始独立事务检查支付状态 - 订单ID: {} ===", orderId);

        try {
            // 1. 先检查本地支付记录
            if (paymentRecordService.isPaid(orderId)) {
                log.info("本地支付记录显示已支付 - 订单ID: {}", orderId);
                return true;
            }

            // 2. 检查本地订单状态
            TOrder order = orderMapper.selectById(orderId);
            if (order != null && TOrderType.STATUS_PAID.equals(order.getStatus())) {
                log.info("本地订单状态显示已支付 - 订单ID: {}", orderId);
                return true;
            }

            // 3. 查询所有待支付的支付记录，检查第三方状态
            List<TPaymentRecord> unpaidRecords = paymentRecordService.getUnpaidRecords(orderId);

            for (TPaymentRecord record : unpaidRecords) {
                try {
                    PaymentType paymentType = PaymentType.valueOf(record.getPaymentMethod().toUpperCase());
                    IPaymentService paymentService = paymentServiceFactory.getPaymentService(paymentType);

                    PaymentQueryResult queryResult = paymentService.queryPaymentStatus(record.getPaymentNo());

                    if (queryResult.isSuccess() && queryResult.isPaid()) {
                        log.warn("发现第三方已支付但本地未同步的订单 - 订单ID: {}, 支付单号: {}",
                                orderId, record.getPaymentNo());

                        // 同步支付状态
                        boolean updated = paymentRecordService.update(
                                record.getOrderId(),
                                record.getPaymentNo(),
                                new BigDecimal(queryResult.getTotalAmount()),
                                TPaymentRecordType.PAYMENT_STATUS_SUCCESS,
                                queryResult.getTradeStatus(),
                                "主动查询发现已支付"
                        );

                        if (!updated) {
                            log.error("支付记录更新失败 - 订单ID: {}, 支付单号: {}", orderId, record.getPaymentNo());
                            return true; // 仍然认为已支付，防止误取消
                        }

                        log.info("支付记录更新成功 - 订单ID: {}, 支付单号: {}", orderId, record.getPaymentNo());

                        // 更新订单状态
                        TOrder updateOrder = new TOrder();
                        updateOrder.setStatus(TOrderType.STATUS_PAID);
                        updateOrder.setUpdateTime(new Date());

                        int orderUpdated = orderMapper.update(updateOrder,
                                Wrappers.lambdaUpdate(TOrder.class)
                                        .eq(TOrder::getId, order.getId())
                                        .eq(TOrder::getStatus, TOrderType.STATUS_WAIT)
                        );

                        if (orderUpdated > 0) {
                            log.info("订单状态更新成功 - 订单ID: {}", orderId);

                            // 记录日志
                            orderLogService.insert(order.getId(), KSLogType.TYPE_PAY,
                                    "主动查询发现已支付 - 交易号: " + queryResult.getTradeNo());

                            log.info("=== 独立事务同步支付状态完成 - 订单ID: {}, 支付单号: {} ===",
                                    orderId, record.getPaymentNo());
                        } else {
                            log.warn("订单状态更新失败，可能已被其他操作修改 - 订单ID: {}", orderId);
                        }

                        return true;
                    }
                } catch (Exception e) {
                    log.error("查询第三方支付状态失败 - 支付单号: {}", record.getPaymentNo(), e);
                }
            }

            log.info("=== 独立事务检查完成，未发现已支付状态 - 订单ID: {} ===", orderId);
            return false;

        } catch (Exception e) {
            log.error("=== 独立事务执行异常 - 订单ID: {} ===", orderId, e);
            throw e;
        }
    }
}
