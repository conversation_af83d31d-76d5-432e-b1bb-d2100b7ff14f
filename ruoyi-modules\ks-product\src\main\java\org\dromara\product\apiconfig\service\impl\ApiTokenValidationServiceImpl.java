package org.dromara.product.apiconfig.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.product.apiconfig.service.IApiTokenValidationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * API Token验证服务实现
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiTokenValidationServiceImpl implements IApiTokenValidationService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public Long getMemberIdByApiToken(String apiToken) {
        if (apiToken == null || apiToken.trim().isEmpty()) {
            log.info("API Token为空");
            return null;
        }

        try {
            // 直接通过SQL查询会员ID
            String sql = "SELECT member_id FROM t_member WHERE api_key = ? AND del_flag = '0' AND status = '0'";

            log.info("查询会员ID: token={}, sql={}", maskToken(apiToken), sql);
            Long memberId = jdbcTemplate.queryForObject(sql, Long.class, apiToken);
            log.info("查询结果: memberId={}", memberId);
            return memberId;
        } catch (Exception e) {
            log.warn("根据API Token查询会员ID失败: token={}, error={}", maskToken(apiToken), e.getMessage());
            return null;
        }
    }

    @Override
    public boolean isValidApiToken(String apiToken) {
        return getMemberIdByApiToken(apiToken) != null;
    }

    /**
     * 脱敏API Token
     */
    private String maskToken(String token) {
        if (token == null || token.length() <= 8) {
            return "****";
        }
        return token.substring(0, 4) + "****" + token.substring(token.length() - 4);
    }
}
