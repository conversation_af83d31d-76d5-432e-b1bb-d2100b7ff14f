<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>附件下载链接修复测试</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            margin: 0 auto;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 附件下载链接修复测试</h1>
        
        <div>
            <button onclick="testVersionCheckAPI()">测试版本检查API</button>
            <button onclick="testAttachmentHandling()">测试附件处理</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            console.log(message);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testVersionCheckAPI() {
            log('🔍 测试版本检查API（修复后）...', 'info');
            
            try {
                const url = 'http://localhost:7013/api/browser-extension/version/check?currentVersion=1.0.2';
                log(`请求URL: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.code === 200 && data.data) {
                        const versionInfo = data.data;
                        
                        // 检查版本信息
                        log(`版本检查结果:`, 'info');
                        log(`  当前版本: ${versionInfo.currentVersion}`, 'info');
                        log(`  最新版本: ${versionInfo.latestVersion}`, 'info');
                        log(`  有更新: ${versionInfo.hasUpdate}`, versionInfo.hasUpdate ? 'warning' : 'success');
                        log(`  下载链接: ${versionInfo.downloadUrl}`, 'info');
                        
                        // 检查下载链接类型
                        if (versionInfo.downloadUrl) {
                            if (versionInfo.downloadUrl.startsWith('http')) {
                                log(`✅ 下载链接有效: ${versionInfo.downloadUrl}`, 'success');
                            } else if (versionInfo.downloadUrl.includes('请联系管理员')) {
                                log(`⚠️ 无下载文件，显示提示信息`, 'warning');
                            } else {
                                log(`❓ 未知的下载链接格式: ${versionInfo.downloadUrl}`, 'warning');
                            }
                        } else {
                            log(`❌ 下载链接为空`, 'error');
                        }
                        
                        // 检查更新说明
                        if (versionInfo.releaseNotes) {
                            log(`更新说明: ${versionInfo.releaseNotes}`, 'info');
                        }
                        
                    } else {
                        log(`❌ API返回错误: ${data.msg}`, 'error');
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ HTTP错误: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
                
                if (error.message.includes('Failed to fetch')) {
                    log('💡 可能原因: 后端服务未启动或网络连接问题', 'warning');
                }
            }
        }

        async function testAttachmentHandling() {
            log('📎 测试不同附件情况的处理...', 'info');
            
            // 模拟不同的测试场景
            const testScenarios = [
                {
                    name: '有效附件ID',
                    description: '模拟有上传文件的情况',
                    expectedResult: 'HTTP下载链接'
                },
                {
                    name: '空附件ID',
                    description: '模拟没有上传文件的情况',
                    expectedResult: '请联系管理员提示'
                },
                {
                    name: '无效附件ID',
                    description: '模拟附件ID存在但文件不存在的情况',
                    expectedResult: '请联系管理员提示'
                }
            ];
            
            testScenarios.forEach((scenario, index) => {
                log(`测试场景 ${index + 1}: ${scenario.name}`, 'info');
                log(`  描述: ${scenario.description}`, 'info');
                log(`  期望结果: ${scenario.expectedResult}`, 'info');
            });
            
            log('💡 实际测试需要调用真实API来验证', 'warning');
            log('💡 建议: 在产品管理中尝试不同的附件配置', 'warning');
        }

        // 页面加载时显示说明
        window.addEventListener('load', () => {
            log('🔧 附件下载链接修复测试页面已加载', 'info');
            log('', 'info');
            log('修复内容:', 'info');
            log('1. 后端直接使用OssService获取下载链接', 'info');
            log('2. 改进了错误处理和日志记录', 'info');
            log('3. 前端UI根据链接类型显示不同按钮', 'info');
            log('', 'info');
            log('测试步骤:', 'info');
            log('1. 确保后端服务运行在localhost:7013', 'info');
            log('2. 在产品管理中配置插件版本和附件', 'info');
            log('3. 点击"测试版本检查API"按钮', 'info');
            log('4. 查看下载链接是否正确获取', 'info');
            log('', 'info');
        });
    </script>
</body>
</html>
