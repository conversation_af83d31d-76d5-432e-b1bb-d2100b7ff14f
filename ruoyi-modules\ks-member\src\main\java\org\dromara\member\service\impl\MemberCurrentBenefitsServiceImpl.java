package org.dromara.member.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.member.domain.BenefitConfig;
import org.dromara.member.domain.BenefitDetail;
import org.dromara.member.domain.entity.TMember;
import org.dromara.member.domain.entity.TMemberCurrentBenefits;
import org.dromara.member.domain.vo.TMemberCurrentBenefitsVo;
import org.dromara.member.mapper.BenefitConfigMapper;
import org.dromara.member.mapper.BenefitDetailMapper;
import org.dromara.member.mapper.TMemberCurrentBenefitsMapper;
import org.dromara.member.mapper.TMemberMapper;
import org.dromara.member.service.IMemberCurrentBenefitsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员当前权益Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MemberCurrentBenefitsServiceImpl implements IMemberCurrentBenefitsService {

    private final TMemberCurrentBenefitsMapper currentBenefitsMapper;
    private final BenefitConfigMapper benefitConfigMapper;
    private final BenefitDetailMapper benefitDetailMapper;
    private final TMemberMapper memberMapper;

    @Override
    public List<TMemberCurrentBenefitsVo> getCurrentBenefits(Long memberId) {
        return currentBenefitsMapper.selectActiveBenefitsByMemberId(memberId)
                .stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());
    }

    @Override
    public List<TMemberCurrentBenefitsVo> getBenefitsByType(Long memberId, String benefitType) {
        return List.of();
    }

    public List<TMemberCurrentBenefitsVo> getBenefitsByConfigId(Long memberId, Long benefitConfigId) {
        return currentBenefitsMapper.selectBenefitsByMemberIdAndConfigId(memberId, benefitConfigId)
                .stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncMemberBenefits(Long memberId) {
        try {
            log.info("开始同步会员权益: memberId={}", memberId);

            // 1. 获取会员信息
            TMember member = memberMapper.selectById(memberId);
            if (member == null) {
                log.error("会员不存在: {}", memberId);
                return false;
            }

            // 2. 清理现有的当前权益
            clearMemberBenefits(memberId);

            // 3. 根据会员等级获取权益配置
            Integer memberLevel = member.getMemberLevel();
            BenefitConfig benefitConfig = benefitConfigMapper.selectOne(
                    Wrappers.<BenefitConfig>lambdaQuery()
                            .eq(BenefitConfig::getMemberLevel, memberLevel)
                            .eq(BenefitConfig::getStatus, 1)
                            .eq(BenefitConfig::getDelFlag, "0")
            );

            if (benefitConfig == null) {
                log.warn("未找到会员等级 {} 的权益配置", memberLevel);
                return true; // 没有权益配置也算成功
            }

            // 4. 获取权益详情
            List<BenefitDetail> benefitDetails = benefitDetailMapper.selectList(
                    Wrappers.<BenefitDetail>lambdaQuery()
                            .eq(BenefitDetail::getConfigId, benefitConfig.getId())
                            .eq(BenefitDetail::getIsEnabled, 1)
                            .eq(BenefitDetail::getDelFlag, "0")
                            .orderByAsc(BenefitDetail::getDisplayOrder)
            );

            // 5. 创建当前权益记录
            Date now = new Date();
            Date expireDate = member.getMembershipExpireDate();

            // 为每个权益详情创建一条记录
            for (BenefitDetail detail : benefitDetails) {
                TMemberCurrentBenefits currentBenefit = new TMemberCurrentBenefits();
                currentBenefit.setMemberId(memberId);
                currentBenefit.setBenefitConfigId(benefitConfig.getId());
                currentBenefit.setSubscriptionId(null); // 暂时为空，后续可以关联订阅
                currentBenefit.setEffectiveTime(now);
                currentBenefit.setExpiryDate(expireDate);
                currentBenefit.setStatus("active");
                currentBenefit.setAutoRenew(0); // 默认不自动续费
                currentBenefit.setSyncStatus(1); // 已同步
                currentBenefit.setLastSyncTime(now);
                currentBenefit.setRemark("系统自动同步");
                currentBenefit.setDelFlag("0");

                currentBenefitsMapper.insert(currentBenefit);
            }

            log.info("会员权益同步成功: memberId={}, 权益数量={}", memberId, benefitDetails.size());
            return true;

        } catch (Exception e) {
            log.error("同步会员权益失败: memberId={}", memberId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncAllMemberBenefits() {
        try {
            log.info("开始批量同步所有会员权益");

            // 获取所有正常状态的会员
            List<TMember> members = memberMapper.selectList(
                    Wrappers.<TMember>lambdaQuery()
                            .eq(TMember::getStatus, "0")
                            .eq(TMember::getDelFlag, "0")
            );

            int successCount = 0;
            int failCount = 0;

            for (TMember member : members) {
                try {
                    if (syncMemberBenefits(member.getMemberId())) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("同步会员权益失败: memberId={}", member.getMemberId(), e);
                    failCount++;
                }
            }

            log.info("批量同步会员权益完成: 总数={}, 成功={}, 失败={}",
                    members.size(), successCount, failCount);
            return failCount == 0;

        } catch (Exception e) {
            log.error("批量同步会员权益失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean clearMemberBenefits(Long memberId) {
        try {
            currentBenefitsMapper.deleteByMemberId(memberId);
            log.debug("清理会员权益成功: memberId={}", memberId);
            return true;
        } catch (Exception e) {
            log.error("清理会员权益失败: memberId={}", memberId, e);
            return false;
        }
    }

    @Override
    public Boolean markExpiredBenefits() {
        try {
            int count = currentBenefitsMapper.markExpiredBenefits();
            log.info("标记过期权益完成: 数量={}", count);
            return true;
        } catch (Exception e) {
            log.error("标记过期权益失败", e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getMemberBenefitsForDisplay(Long memberId) {
        List<TMemberCurrentBenefits> benefits = currentBenefitsMapper.selectActiveBenefitsByMemberId(memberId);

        return benefits.stream().map(benefit -> {
            Map<String, Object> map = new HashMap<>();

            // 基本信息
            map.put("id", benefit.getId());
            map.put("benefitConfigId", benefit.getBenefitConfigId());
            map.put("effectiveDate", benefit.getEffectiveTime());
            map.put("expiryDate", benefit.getExpiryDate());
            map.put("status", benefit.getStatus());

            // TODO: 根据 benefitConfigId 查询权益配置详情
            // 暂时返回默认值，后续需要关联查询权益配置表
            map.put("type", "unknown");
            map.put("name", "权益配置ID: " + benefit.getBenefitConfigId());
            map.put("value", "请配置权益详情");
            map.put("config", "{}");

            return map;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean hasBenefit(Long memberId, String benefitType, String benefitName) {
        // TODO: 需要重新实现，根据 benefitConfigId 查询权益配置
        List<TMemberCurrentBenefits> benefits = currentBenefitsMapper.selectActiveBenefitsByMemberId(memberId);
        return !benefits.isEmpty(); // 暂时简单判断是否有权益
    }

    @Override
    public String getBenefitConfig(Long memberId, String benefitType, String benefitName) {
        // TODO: 需要重新实现，根据 benefitConfigId 查询权益配置
        return "{}"; // 暂时返回空配置
    }

    /**
     * 转换为VO对象
     */
    private TMemberCurrentBenefitsVo convertToVo(TMemberCurrentBenefits entity) {
        TMemberCurrentBenefitsVo vo = new TMemberCurrentBenefitsVo();
        vo.setId(entity.getId());
        vo.setMemberId(entity.getMemberId());
        vo.setBenefitConfigId(entity.getBenefitConfigId());
        vo.setSubscriptionId(entity.getSubscriptionId());
        vo.setEffectiveTime(entity.getEffectiveTime());
        vo.setExpiryDate(entity.getExpiryDate());
        vo.setStatus(entity.getStatus());
        vo.setAutoRenew(entity.getAutoRenew());
        vo.setSyncStatus(entity.getSyncStatus());
        vo.setLastSyncTime(entity.getLastSyncTime());
        vo.setRemark(entity.getRemark());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());

        // TODO: 根据 benefitConfigId 查询权益配置详情，填充权益名称、类型等信息
        // 这里暂时返回基本信息，后续需要关联查询权益配置表
        vo.setBenefitType("unknown");
        vo.setBenefitName("权益配置ID: " + entity.getBenefitConfigId());
        vo.setBenefitConfig("{}");
        vo.setBenefitValue("请配置权益详情");

        return vo;
    }

    /**
     * 从权益配置JSON中提取简化的权益值
     */
    private String extractBenefitValue(String benefitConfig) {
        if (benefitConfig == null || benefitConfig.trim().isEmpty()) {
            return "";
        }

        try {
            JSONObject config = JSONUtil.parseObj(benefitConfig);

            // 根据不同的权益类型提取关键值
            if (config.containsKey("daily_limit")) {
                return config.getStr("daily_limit") + "次/天";
            } else if (config.containsKey("rate_limit")) {
                return config.getStr("rate_limit");
            } else if (config.containsKey("support_type")) {
                return config.getStr("support_type");
            } else if (config.containsKey("response_time")) {
                return config.getStr("response_time");
            } else {
                // 返回第一个值
                return config.values().iterator().next().toString();
            }
        } catch (Exception e) {
            log.warn("解析权益配置失败: {}", benefitConfig, e);
            return benefitConfig;
        }
    }

}
