package org.dromara.product.apiconfig.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.stereotype.Service;
import org.dromara.product.apiconfig.domain.bo.TApiCallLogBo;
import org.dromara.product.apiconfig.domain.vo.TApiCallLogVo;
import org.dromara.product.apiconfig.domain.TApiCallLog;
import org.dromara.product.apiconfig.domain.TApiConfig;
import org.dromara.product.apiconfig.mapper.TApiCallLogMapper;
import org.dromara.product.apiconfig.service.ITApiCallLogService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * API调用日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TApiCallLogServiceImpl implements ITApiCallLogService {

    private final TApiCallLogMapper baseMapper;

    /**
     * 查询API调用日志
     */
    @Override
    public TApiCallLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询API调用日志列表
     */
    @Override
    public TableDataInfo<TApiCallLogVo> queryPageList(TApiCallLogBo bo, PageQuery pageQuery) {
        MPJLambdaWrapper<TApiCallLog> lqw = buildQueryWrapper(bo);
        Page<TApiCallLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 处理额外字段
        for (TApiCallLogVo vo : result.getRecords()) {
            processVo(vo);
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询API调用日志列表
     */
    @Override
    public List<TApiCallLogVo> queryList(TApiCallLogBo bo) {
        MPJLambdaWrapper<TApiCallLog> lqw = buildQueryWrapper(bo);
        List<TApiCallLogVo> result = baseMapper.selectVoList(lqw);

        // 处理额外字段
        for (TApiCallLogVo vo : result) {
            processVo(vo);
        }

        return result;
    }

    private MPJLambdaWrapper<TApiCallLog> buildQueryWrapper(TApiCallLogBo bo) {
        Map<String, Object> params = bo.getParams();
        MPJLambdaWrapper<TApiCallLog> lqw = JoinWrappers.lambda(TApiCallLog.class)
            .disableSubLogicDel()
            .selectAll(TApiCallLog.class)
            .selectAs(TApiConfig::getApiCode, "api_code")
            .selectAs(TApiConfig::getApiName, "api_name")
            .selectAs(TApiConfig::getHttpMethod, "http_method")
            .selectAs(TApiConfig::getBaseUrl, "request_url")
            .leftJoin(TApiConfig.class, TApiConfig::getId, TApiCallLog::getApiConfigId);

        lqw.eq(StringUtils.isNotBlank(bo.getTraceId()), TApiCallLog::getTraceId, bo.getTraceId());
        lqw.eq(bo.getApiConfigId() != null, TApiCallLog::getApiConfigId, bo.getApiConfigId());
        lqw.eq(bo.getUserId() != null, TApiCallLog::getUserId, bo.getUserId());
        lqw.eq(bo.getResponseStatus() != null, TApiCallLog::getResponseStatus, bo.getResponseStatus());
        lqw.like(StringUtils.isNotBlank(bo.getErrorMessage()), TApiCallLog::getErrorMessage, bo.getErrorMessage());
        lqw.eq(StringUtils.isNotBlank(bo.getClientIp()), TApiCallLog::getClientIp, bo.getClientIp());
        lqw.like(StringUtils.isNotBlank(bo.getUserAgent()), TApiCallLog::getUserAgent, bo.getUserAgent());

        // API相关查询
        lqw.like(StringUtils.isNotBlank(bo.getApiCode()), TApiConfig::getApiCode, bo.getApiCode());
        lqw.like(StringUtils.isNotBlank(bo.getApiName()), TApiConfig::getApiName, bo.getApiName());

        // 时间范围查询
        if (bo.getCreateTimeStart() != null) {
            lqw.ge(TApiCallLog::getCreateTime, bo.getCreateTimeStart());
        }
        if (bo.getCreateTimeEnd() != null) {
            lqw.le(TApiCallLog::getCreateTime, bo.getCreateTimeEnd());
        }

        // 成功状态查询
        if (bo.getSuccess() != null) {
            if (bo.getSuccess()) {
                lqw.ge(TApiCallLog::getResponseStatus, 200).lt(TApiCallLog::getResponseStatus, 300);
            } else {
                lqw.and(wrapper -> wrapper.lt(TApiCallLog::getResponseStatus, 200).or().ge(TApiCallLog::getResponseStatus, 300));
            }
        }

        lqw.orderByDesc(TApiCallLog::getCreateTime);
        return lqw;
    }

    /**
     * 处理VO对象，添加额外字段
     */
    private void processVo(TApiCallLogVo vo) {
        // 判断是否成功
        if (vo.getResponseStatus() != null) {
            vo.setSuccess(vo.getResponseStatus() >= 200 && vo.getResponseStatus() < 300);
            vo.setStatusDesc(getStatusDesc(vo.getResponseStatus()));
        }

        // 计算响应大小
        if (vo.getResponseData() != null) {
            vo.setResponseSize((long) vo.getResponseData().toString().getBytes().length);
        }
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) return "未知";
        if (status >= 200 && status < 300) return "成功";
        if (status >= 400 && status < 500) return "客户端错误";
        if (status >= 500) return "服务器错误";
        return "其他";
    }

    /**
     * 新增API调用日志
     */
    @Override
    public Boolean insertByBo(TApiCallLogBo bo) {
        TApiCallLog add = MapstructUtils.convert(bo, TApiCallLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改API调用日志
     */
    @Override
    public Boolean updateByBo(TApiCallLogBo bo) {
        TApiCallLog update = MapstructUtils.convert(bo, TApiCallLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TApiCallLog entity){
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除API调用日志
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取API调用统计信息
     */
    @Override
    public Object getApiCallStats(TApiCallLogBo bo) {
        // TODO 实现统计逻辑
        return null;
    }

    /**
     * 清理过期日志
     */
    @Override
    public Boolean cleanExpiredLogs(Integer days) {
        if (days == null || days <= 0) {
            days = 30; // 默认清理30天前的日志
        }

        LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
        MPJLambdaWrapper<TApiCallLog> wrapper = JoinWrappers.lambda(TApiCallLog.class);
        wrapper.lt(TApiCallLog::getCreateTime, expireTime);

        return baseMapper.delete(wrapper) >= 0;
    }
}
