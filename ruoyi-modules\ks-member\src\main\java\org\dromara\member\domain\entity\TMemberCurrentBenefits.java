package org.dromara.member.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 会员当前权益对象 t_member_current_benefits
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_member_current_benefits")
public class TMemberCurrentBenefits extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 会员ID
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 权益配置ID
     */
    @TableField("benefit_config_id")
    private Long benefitConfigId;

    /**
     * 订阅ID
     */
    @TableField("subscription_id")
    private Long subscriptionId;

    /**
     * 生效时间
     */
    @TableField("effective_time")
    private Date effectiveTime;

    /**
     * 过期时间
     */
    @TableField("expiry_date")
    private Date expiryDate;

    /**
     * 状态(active=生效中 expired=已过期 disabled=已禁用)
     */
    @TableField("status")
    private String status;

    /**
     * 自动续费(0=否 1=是)
     */
    @TableField("auto_renew")
    private Integer autoRenew;

    /**
     * 同步状态(0=未同步 1=已同步)
     */
    @TableField("sync_status")
    private Integer syncStatus;

    /**
     * 最后同步时间
     */
    @TableField("last_sync_time")
    private Date lastSyncTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 删除标志(0=存在 1=删除)
     */
    @TableField("del_flag")
    @TableLogic
    private String delFlag;

}
