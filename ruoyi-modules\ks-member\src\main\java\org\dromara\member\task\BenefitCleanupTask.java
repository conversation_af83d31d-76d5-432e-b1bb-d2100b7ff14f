package org.dromara.member.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.member.service.IMemberCurrentBenefitsService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 权益清理定时任务
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class BenefitCleanupTask {

    private final IMemberCurrentBenefitsService memberCurrentBenefitsService;

    /**
     * 每小时清理过期权益
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void cleanupExpiredBenefits() {
        log.info("开始清理过期权益");
        try {
            Boolean result = memberCurrentBenefitsService.markExpiredBenefits();
            if (result) {
                log.info("过期权益清理完成");
            } else {
                log.warn("过期权益清理失败");
            }
        } catch (Exception e) {
            log.error("过期权益清理异常", e);
        }
    }

}
