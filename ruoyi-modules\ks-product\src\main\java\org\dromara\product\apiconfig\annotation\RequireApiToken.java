package org.dromara.product.apiconfig.annotation;

import java.lang.annotation.*;

/**
 * API Token验证注解
 * 用于标记需要API Token验证的接口
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireApiToken {
    
    /**
     * 是否检查会员状态
     */
    boolean checkMemberStatus() default true;
    
    /**
     * 是否启用访问频率限制
     */
    boolean enableRateLimit() default false;
    
    /**
     * 访问频率限制配置
     * 格式：窗口时间(秒):最大请求数，如："60:100"表示60秒内最多100次请求
     */
    String rateLimitConfig() default "60:100";
}
