package org.dromara.product.apiconfig.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.product.apiconfig.domain.TApiCallLog;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * API调用日志视图对象 t_api_call_log
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TApiCallLog.class)
public class TApiCallLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 链路追踪ID
     */
    @ExcelProperty(value = "链路追踪ID")
    private String traceId;

    /**
     * API配置ID
     */
    @ExcelProperty(value = "API配置ID")
    private Long apiConfigId;

    /**
     * API代码
     */
    @ExcelProperty(value = "API代码")
    private String apiCode;

    /**
     * API名称
     */
    @ExcelProperty(value = "API名称")
    private String apiName;

    /**
     * 调用用户ID
     */
    @ExcelProperty(value = "调用用户ID")
    private Long userId;

    /**
     * 调用用户名
     */
    @ExcelProperty(value = "调用用户")
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "userId")
    private String userName;

    /**
     * 请求参数
     */
    private Map<String, Object> requestParams;

    /**
     * 请求头
     */
    private Map<String, Object> requestHeaders;

    /**
     * 响应数据
     */
    private Object responseData;

    /**
     * HTTP响应状态码
     */
    @ExcelProperty(value = "响应状态码")
    private Integer responseStatus;

    /**
     * 执行时间（毫秒）
     */
    @ExcelProperty(value = "执行时间(ms)")
    private Integer executionTimeMs;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 客户端IP
     */
    @ExcelProperty(value = "客户端IP")
    private String clientIp;

    /**
     * 用户代理
     */
    @ExcelProperty(value = "用户代理")
    private String userAgent;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "调用时间")
    private LocalDateTime createTime;

    /**
     * 是否成功
     */
    @ExcelProperty(value = "是否成功")
    private Boolean success;

    /**
     * 状态描述
     */
    @ExcelProperty(value = "状态")
    private String statusDesc;

    /**
     * 响应大小（字节）
     */
    private Long responseSize;

    /**
     * 请求方法
     */
    @ExcelProperty(value = "请求方法")
    private String httpMethod;

    /**
     * 请求URL
     */
    @ExcelProperty(value = "请求URL")
    private String requestUrl;
}
