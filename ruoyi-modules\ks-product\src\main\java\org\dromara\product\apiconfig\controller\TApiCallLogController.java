package org.dromara.product.apiconfig.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.product.apiconfig.domain.vo.TApiCallLogVo;
import org.dromara.product.apiconfig.domain.bo.TApiCallLogBo;
import org.dromara.product.apiconfig.service.ITApiCallLogService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * API调用日志
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/product/apiCallLog")
public class TApiCallLogController extends BaseController {

    private final ITApiCallLogService tApiCallLogService;

    /**
     * 查询API调用日志列表
     */
    @SaCheckPermission("product:apiCallLog:list")
    @GetMapping("/list")
    public TableDataInfo<TApiCallLogVo> list(TApiCallLogBo bo, PageQuery pageQuery) {
        return tApiCallLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出API调用日志列表
     */
    @SaCheckPermission("product:apiCallLog:export")
    @Log(title = "API调用日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TApiCallLogBo bo, HttpServletResponse response) {
        List<TApiCallLogVo> list = tApiCallLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "API调用日志", TApiCallLogVo.class, response);
    }

    /**
     * 获取API调用日志详细信息
     */
    @SaCheckPermission("product:apiCallLog:query")
    @GetMapping("/{id}")
    public R<TApiCallLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(tApiCallLogService.queryById(id));
    }

    /**
     * 新增API调用日志
     */
    @SaCheckPermission("product:apiCallLog:add")
    @Log(title = "API调用日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TApiCallLogBo bo) {
        return toAjax(tApiCallLogService.insertByBo(bo));
    }

    /**
     * 修改API调用日志
     */
    @SaCheckPermission("product:apiCallLog:edit")
    @Log(title = "API调用日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TApiCallLogBo bo) {
        return toAjax(tApiCallLogService.updateByBo(bo));
    }

    /**
     * 删除API调用日志
     */
    @SaCheckPermission("product:apiCallLog:remove")
    @Log(title = "API调用日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(tApiCallLogService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取API调用统计信息
     */
    @SaCheckPermission("product:apiCallLog:stats")
    @GetMapping("/stats")
    public R<Object> getStats(TApiCallLogBo bo) {
        return R.ok(tApiCallLogService.getApiCallStats(bo));
    }

    /**
     * 清理过期日志
     */
    @SaCheckPermission("product:apiCallLog:clean")
    @Log(title = "API调用日志", businessType = BusinessType.CLEAN)
    @PostMapping("/clean")
    public R<Void> cleanExpiredLogs(@RequestParam(defaultValue = "30") Integer days) {
        return toAjax(tApiCallLogService.cleanExpiredLogs(days));
    }
}
