package org.dromara.web.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import org.dromara.common.core.domain.R;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.social.config.properties.SocialLoginConfigProperties;
import org.dromara.common.social.config.properties.SocialProperties;
import org.dromara.common.social.utils.SocialUtils;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 飞书OAuth授权控制器
 * 专门处理浏览器插件的飞书OAuth授权流程
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@SaIgnore
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth/feishu/oauth")
public class FeishuOAuthController extends BaseController {

    private final SocialProperties socialProperties;

    /**
     * Redis缓存键前缀
     */
    private static final String FEISHU_AUTH_STATUS_PREFIX = "feishu:oauth:status:";
    private static final Duration FEISHU_AUTH_STATUS_EXPIRE = Duration.ofMinutes(10);

    /**
     * 获取飞书OAuth授权URL
     * 专为浏览器插件提供的飞书授权接口
     *
     * @return 授权URL和会话ID
     */
    @PostMapping("/auth-url")
    public R<Map<String, Object>> getAuthUrl() {
        try {
            log.info("🔄 生成飞书OAuth授权URL");

            // 检查飞书配置
            SocialLoginConfigProperties feishuConfig = socialProperties.getType().get("feishu");
            if (ObjectUtil.isNull(feishuConfig)) {
                return R.fail("飞书平台配置不存在");
            }

            // 生成会话ID
            String sessionId = "feishu_auth_" + System.currentTimeMillis() + "_" +
                              java.util.UUID.randomUUID().toString().substring(0, 8);

            // 使用JustAuth获取AuthRequest
            AuthRequest authRequest = SocialUtils.getAuthRequest("feishu", socialProperties);

            // 生成授权URL
            String authUrl = authRequest.authorize(sessionId);

            Map<String, Object> result = new HashMap<>();
            result.put("auth_url", authUrl);
            result.put("session_id", sessionId);

            log.info("✅ 飞书OAuth授权URL生成成功: sessionId={}", sessionId);
            return R.ok(result);

        } catch (Exception e) {
            log.error("❌ 生成飞书OAuth授权URL异常", e);
            return R.fail("生成授权URL失败: " + e.getMessage());
        }
    }

    /**
     * 飞书OAuth回调接口
     * 处理飞书授权回调，专为浏览器插件使用
     *
     * @param code 授权码
     * @param state 状态参数（会话ID）
     * @param error 错误信息
     * @param error_description 错误描述
     * @return 回调处理结果页面
     */
    @GetMapping("/callback")
    public String handleCallback(
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String error,
            @RequestParam(required = false) String error_description) {

        try {
            log.info("🔄 接收到飞书OAuth回调: code={}, state={}, error={}",
                    code != null ? "***" : null, state, error);

            // 检查是否有错误
            if (error != null) {
                log.error("❌ 飞书OAuth授权失败: error={}, description={}", error, error_description);
                handleAuthError(state, "授权失败: " + error_description);
                return generateCallbackPage(false, "授权失败: " + error_description);
            }

            // 检查必要参数
            if (code == null || state == null) {
                log.error("❌ 飞书OAuth回调参数不完整: code={}, state={}", code, state);
                handleAuthError(state, "缺少必要参数");
                return generateCallbackPage(false, "回调参数不完整");
            }

            // 使用JustAuth处理回调
            AuthRequest authRequest = SocialUtils.getAuthRequest("feishu", socialProperties);
            AuthCallback authCallback = new AuthCallback();
            authCallback.setCode(code);
            authCallback.setState(state);

            AuthResponse<AuthUser> response = authRequest.login(authCallback);

            if (response.ok()) {
                AuthUser authUser = response.getData();
                String accessToken = authUser.getToken().getAccessToken();

                // 存储授权成功状态
                handleAuthSuccess(state, accessToken, authUser);
                log.info("✅ 飞书OAuth授权成功: state={}, user={}", state, authUser.getUsername());

                return generateCallbackPage(true, "授权成功，请关闭此窗口");
            } else {
                log.error("❌ 飞书OAuth登录失败: {}", response.getMsg());
                handleAuthError(state, response.getMsg());
                return generateCallbackPage(false, "登录失败: " + response.getMsg());
            }

        } catch (Exception e) {
            log.error("❌ 处理飞书OAuth回调异常: state={}", state, e);
            handleAuthError(state, "服务器错误: " + e.getMessage());
            return generateCallbackPage(false, "服务器错误: " + e.getMessage());
        }
    }

    /**
     * 查询飞书OAuth授权状态
     * 供浏览器插件轮询检查授权是否完成
     *
     * @param requestBody 包含sessionId的请求体
     * @return 授权状态信息
     */
    @PostMapping("/status")
    public R<Map<String, Object>> checkAuthStatus(@RequestBody Map<String, Object> requestBody) {
        try {
            String sessionId = (String) requestBody.get("sessionId");

            if (sessionId == null || sessionId.trim().isEmpty()) {
                return R.fail("会话ID不能为空");
            }

            log.debug("🔍 检查飞书OAuth授权状态: sessionId={}", sessionId);

            Map<String, Object> status = getAuthStatus(sessionId);
            return R.ok(status);

        } catch (Exception e) {
            log.error("❌ 检查飞书OAuth授权状态异常", e);
            return R.fail("检查授权状态失败: " + e.getMessage());
        }
    }

    /**
     * 清除飞书OAuth授权状态
     * 用于清理过期或无效的授权状态
     *
     * @param requestBody 包含sessionId的请求体
     * @return 清理结果
     */
    @PostMapping("/clear")
    public R<Void> clearAuthStatus(@RequestBody Map<String, Object> requestBody) {
        try {
            String sessionId = (String) requestBody.get("sessionId");

            if (sessionId == null || sessionId.trim().isEmpty()) {
                return R.fail("会话ID不能为空");
            }

            log.info("🗑️ 清除飞书OAuth授权状态: sessionId={}", sessionId);

            String statusKey = FEISHU_AUTH_STATUS_PREFIX + sessionId;
            RedisUtils.deleteObject(statusKey);

            return R.ok();

        } catch (Exception e) {
            log.error("❌ 清除飞书OAuth授权状态异常", e);
            return R.fail("清除授权状态失败: " + e.getMessage());
        }
    }

    // ========== 辅助方法 ==========

    /**
     * 处理飞书授权成功
     */
    private void handleAuthSuccess(String sessionId, String accessToken, AuthUser authUser) {
        String statusKey = FEISHU_AUTH_STATUS_PREFIX + sessionId;
        Map<String, Object> status = new HashMap<>();
        status.put("success", true);
        status.put("token", accessToken);
        status.put("error", null);
        status.put("user_info", Map.of(
                "username", authUser.getUsername() != null ? authUser.getUsername() : "",
                "nickname", authUser.getNickname() != null ? authUser.getNickname() : "",
                "avatar", authUser.getAvatar() != null ? authUser.getAvatar() : "",
                "email", authUser.getEmail() != null ? authUser.getEmail() : ""
        ));
        status.put("timestamp", System.currentTimeMillis());

        RedisUtils.setCacheObject(statusKey, status, FEISHU_AUTH_STATUS_EXPIRE);
    }

    /**
     * 处理飞书授权失败
     */
    private void handleAuthError(String sessionId, String errorMessage) {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            log.warn("⚠️ 状态参数为空，无法记录授权失败状态");
            return;
        }

        String statusKey = FEISHU_AUTH_STATUS_PREFIX + sessionId;
        Map<String, Object> status = new HashMap<>();
        status.put("success", false);
        status.put("token", null);
        status.put("error", errorMessage);
        status.put("timestamp", System.currentTimeMillis());

        RedisUtils.setCacheObject(statusKey, status, FEISHU_AUTH_STATUS_EXPIRE);
    }

    /**
     * 获取飞书授权状态
     */
    private Map<String, Object> getAuthStatus(String sessionId) {
        String statusKey = FEISHU_AUTH_STATUS_PREFIX + sessionId;
        Map<String, Object> status = RedisUtils.getCacheObject(statusKey);

        if (status == null) {
            // 返回等待状态
            Map<String, Object> waitingStatus = new HashMap<>();
            waitingStatus.put("success", false);
            waitingStatus.put("token", null);
            waitingStatus.put("error", null);
            waitingStatus.put("waiting", true);
            return waitingStatus;
        }

        return status;
    }

    /**
     * 生成飞书回调页面HTML
     */
    private String generateCallbackPage(boolean success, String message) {
        String title = success ? "授权成功" : "授权失败";
        String iconColor = success ? "#52c41a" : "#ff4d4f";
        String icon = success ? "✅" : "❌";

        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>%s - 飞书授权</title>
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 100vh;
                        margin: 0;
                        background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%);
                    }
                    .container {
                        background: white;
                        padding: 40px;
                        border-radius: 12px;
                        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                        text-align: center;
                        max-width: 400px;
                        width: 90%%;
                    }
                    .icon {
                        font-size: 48px;
                        color: %s;
                        margin-bottom: 20px;
                    }
                    .title {
                        font-size: 24px;
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 16px;
                    }
                    .message {
                        font-size: 16px;
                        color: #666;
                        margin-bottom: 30px;
                        line-height: 1.5;
                    }
                    .close-btn {
                        background: %s;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 6px;
                        font-size: 16px;
                        cursor: pointer;
                        transition: opacity 0.2s;
                    }
                    .close-btn:hover {
                        opacity: 0.8;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="icon">%s</div>
                    <div class="title">%s</div>
                    <div class="message">%s</div>
                    <button class="close-btn" onclick="window.close()">关闭窗口</button>
                </div>
                <script>
                    // 3秒后自动关闭窗口
                    setTimeout(function() {
                        window.close();
                    }, 3000);
                </script>
            </body>
            </html>
            """, title, iconColor, iconColor, icon, title, message);
    }
}
